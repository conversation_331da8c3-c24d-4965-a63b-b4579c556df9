"use client";

import { BubbleTable } from "@/components/ui/bubble-table";
import { TableText } from "@/lib/utils/table/table-text";
import type { ColumnSchema, HalveningData } from "@/types";
import type { ColumnDef } from "@tanstack/react-table";
import moment from "moment";
import { useMemo } from "react";

export default function TokenomicsTable({ data }: { data: HalveningData[] }) {
	const columns: ColumnDef<any, any>[] = useMemo<ColumnSchema[]>(
		() => [
			{
				id: "label",
				header: "",
				cell: (info) => <TableText info={info} />,
				enableSorting: false,
			},
			{
				id: "total",
				header: "Total In Circulation (τ)",
				cell: (info) => <TableText info={info} />,
				sortingFn: "alphanumeric",
			},
			{
				id: "issue",
				header: "Issued This H (τ)",
				cell: (info) => <TableText info={info} />,
				sortingFn: "alphanumeric",
			},
			{
				id: "reward",
				header: "Block Reward (τ)",
				cell: (info) => <TableText info={info} />,
				sortingFn: "alphanumeric",
			},
			{
				id: "blocks",
				header: "Duration (Blocks)",
				cell: (info) => <TableText info={info} dividedBy={1} />,
				sortingFn: "alphanumeric",
			},
			{
				id: "duration",
				header: "Duration (S)	",
				cell: (info) => <TableText info={info} dividedBy={1} />,
			},
			{
				id: "time",
				header: "Time (Epoch)	",
				cell: (info) => <TableText info={info} />,
			},
			{
				id: "timestamp",
				header: "Date",
				cell: (info) => <TableText info={info} />,
			},
		],
		[]
	);

	const tableData = useMemo(
		() =>
			data.map((item) => ({
				...item,
				timestamp: moment.utc((item.time ?? 0) * 1000)
					.format("DD MMMM YYYY"),
			})),
		[data]
	);

	return (
		<BubbleTable columnSchemas={columns} rowData={tableData} isManual={false} />
	);
}
