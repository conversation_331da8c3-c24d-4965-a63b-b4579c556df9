import { Separator } from '@repo/ui/components';
import HalveningSection from './halvening-section';
import TokenomicsSupplySection from './tokenomics-supply';
import TokenomicsTable from './tokenomics-table';
import { fetchStats } from '@/api-proxy/stats';
import { taoDivider } from '@repo/ui/lib';

export default async function TokenomicsRootSection() {
  const tokenomicsResponse = await Promise.allSettled([fetchStats()]);

  const bundleStats =
    tokenomicsResponse[0].status === 'fulfilled'
      ? tokenomicsResponse[0].value.data[0]
      : null;

  const circulatingSupply = Number(bundleStats?.issued ?? '0') / taoDivider;
  const blocksPerHalvening = 10500000;
  const secondsPerHalvening = blocksPerHalvening * 12;
  const blocks = Math.floor(blocksPerHalvening - circulatingSupply);

  const halveningData: HalveningData[] = [
    {
      label: 'CURR',
      total: circulatingSupply.toLocaleString(),
      issue: circulatingSupply.toLocaleString(),
      reward: '1',
      blocks,
      duration: blocks * 12,
      time: Math.floor(Date.now() / 1000),
    },
    {
      label: 'H1',
      total: '10,500,000',
      issue: '10,500,000',
      reward: '0.5',
    },
    {
      label: 'H2',
      total: '15,750,000',
      issue: '5,250,000',
      reward: '0.25',
    },
    {
      label: 'H3',
      total: '18,375,000',
      issue: '2,625,000',
      reward: '0.125',
    },
    {
      label: 'H4',
      total: '19,687,500',
      issue: '1,312,500',
      reward: '0.0625',
    },
    {
      label: 'H5',
      total: '20,343,750',
      issue: '656,250',
      reward: '0.03125',
    },
    {
      label: 'H6',
      total: '20,671,875',
      issue: '328,125',
      reward: '0.015625',
    },
    {
      label: 'H7',
      total: '20,835,937.5',
      issue: '164,062.5',
      reward: '0.0078125',
    },
    {
      label: 'H8',
      total: '20,917,968.75',
      issue: '82,031.25',
      reward: '0.00390625',
    },
    {
      label: 'H9',
      total: '20,958,984.375',
      issue: '41,015.625',
      reward: '0.001953125',
    },
    {
      label: 'H10',
      total: '20,979,492.1875',
      issue: '20,507.8125',
      reward: '0.0009765625',
    },
    {
      label: 'H11',
      total: '20,989,746.09375',
      issue: '10,253.90625',
      reward: '0.00048828125',
    },
    {
      label: 'H12',
      total: '20,994,873.046875',
      issue: '5,126.953125',
      reward: '0.000244140625',
    },
  ];

  for (let i = 1; i < halveningData.length; i++) {
    halveningData[i].blocks = blocksPerHalvening;
    halveningData[i].duration = secondsPerHalvening;
    halveningData[i].time =
      (halveningData[i - 1]?.time ?? 0) + (halveningData[i - 1]?.duration ?? 0);
  }

  return (
    <div className='md:gap-18 m-auto flex max-w-7xl flex-col gap-8 sm:gap-10'>
      <TokenomicsSupplySection chain={bundleStats} />
      <Separator />
      <HalveningSection
        data={halveningData}
        circulatingSupply={circulatingSupply}
      />
      <TokenomicsTable data={halveningData} />
    </div>
  );
}
