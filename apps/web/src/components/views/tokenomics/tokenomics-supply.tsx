'use client';

import type { Stats } from '@repo/types/website-api-types';
import { Separator, Text } from '@repo/ui/components';
import { taoDivider } from '@repo/ui/lib';
import { TokenomicsCard } from './tokenomics-card';
import TokenomicGraph from './tokenomics-chart';
import { TOTAL_SUPPLY } from '@/lib/config';

export default function TokenomicsSupplySection({
  chain,
}: {
  chain: Stats | null;
}) {
  const totalSupply = Number(TOTAL_SUPPLY) / taoDivider;
  const circulatingSupply = Math.floor(
    Number(chain?.issued ?? '0') / taoDivider
  );
  const unissued = totalSupply - Number(circulatingSupply);
  const stakedCirculatingSupply = Number(chain?.staked ?? '0') / taoDivider;
  const freeCirculatingSupply = Number(chain?.free ?? 0) / taoDivider;

  const description = [
    'There was an initial iteration of the network name <PERSON><PERSON><PERSON> which was started on the 3rd January 2021 and then halted in the middle of May so that some issues could be addressed. The blockchain and all 546,113 previously mined TAO were migrated to Nakamoto which was started on 2nd November 2021 from block 0. The Finney network was officially launched on the 20th March 2023.',
    'The total token issuance rather than the block number is used to determine the exact point the halvening occurs. As Tao used to recycle registrations is burned back into the unissued supply, there is a lengthening halvening data however this data is calculated at the current block/issuance so will update automatically over time.',
  ];

  const metaInfo = [
    {
      header: 'Circulating Supply',
      content: `${circulatingSupply}`,
    },
    {
      header: 'Total Supply',
      content: `${totalSupply}`,
    },
  ];

  return (
    <div className='gap-7.5 flex flex-col'>
      <p className='text-3xxl md:text-4xxl font-medium'>Tokenomics</p>
      <div className='gap-4.5 flex flex-wrap'>
        {metaInfo.map((item, index) => (
          <TokenomicsCard {...item} key={index} isAnimate />
        ))}
      </div>
      <Separator />
      <div className='flex flex-col gap-6 sm:gap-8 md:gap-14 xl:flex-row'>
        <TokenomicGraph
          totalSupply={totalSupply}
          circulatingSupply={Number(chain?.issued ?? 1) / taoDivider}
          unissued={unissued}
          stakedCirculatingSupply={stakedCirculatingSupply}
          freeCirculatingSupply={freeCirculatingSupply}
          lockedCirculatingSupply={
            Number(chain?.subnet_locked ?? 0) / taoDivider
          }
        />
        <div className='gap-7.5 flex flex-1 flex-col lg:flex-row xl:flex-col'>
          {description.map((item) => (
            <Text level='base' key={item}>
              {item}
            </Text>
          ))}
        </div>
      </div>
    </div>
  );
}
