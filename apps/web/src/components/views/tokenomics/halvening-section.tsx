import { taoDivider, TOTAL_SUPPLY } from "@/lib/config";
import type { HalveningData, TokenomicCardProps } from "@/types";
import moment from "moment";
import { TokenomicsCard } from "./tokenomics-card";
import Halvening<PERSON>hart from "./halvening-chart";

export default function HalveningSection({
	data,
	circulatingSupply,
}: {
  data: HalveningData[];
  circulatingSupply: number;
}) {
	const totalSupply = Number(TOTAL_SUPPLY) / taoDivider;

	const metaCards: TokenomicCardProps[] = [
		{
			header: "Next Halvening",
			content: moment.utc((data?.[1].time ?? 0) * 1000)
				.format("DD MMM YYYY"),
			variant: "text",
		},
		{
			header: "Circulating Supply",
			content: Math.floor(circulatingSupply).toLocaleString("en-US"),
		},
		{
			header: "Total Supply",
			content: `${totalSupply.toLocaleString()}`,
		},
		{
			header: "In Circulation",
			content: ((circulatingSupply * 100) / totalSupply).toLocaleString(
				"en-US",
				{
					minimumFractionDigits: 0,
					maximumFractionDigits: 2,
				}
			),
			variant: "percentage",
		},
	];

	return (
		<div className="flex flex-col gap-9">
			<p className="text-3xl sm:px-7 sm:py-1 sm:text-3xxl">Halvening Data</p>
			<div className="flex flex-wrap gap-5">
				{metaCards.map((item, index) => (
					<TokenomicsCard {...item} key={index} />
				))}
			</div>
			<HalveningChart data={data} />
		</div>
	);
}
