import { Text } from "@/components/elements/typography";
import { Bittensor } from "@/components/ui/bittensor";
import cn from "@/lib/cn";
import type { TokenomicCardProps } from "@/types";
import CountUp from "react-countup";

export function TokenomicsCard({
	header,
	content,
	isAnimate,
	variant = "tao",
}: TokenomicCardProps) {
	return (
		<div
			className={cn(
				"flex flex-col gap-2 rounded-[10px] border border-[#95959533] bg-[#2E2E2E33] px-5 py-4 min-[400px]:gap-3 sm:px-7 sm:py-6",
				isAnimate ? "min-w-[288px]" : "min-w-[248px]"
			)}
		>
			<Text level={"base"} className="text-[#9E9E9E] text-sm min-[400px]:text-base">
				{header}
			</Text>
			<div className="flex items-center sm:gap-1">
				{isAnimate ? (
					<CountUp
						start={0}
						end={Number(content)}
						duration={3}
						className="text-2xl min-[400px]:text-3xl sm:text-3xxl"
					/>
				) : (
					<p className="whitespace-nowrap text-2xl text-white min-[400px]:text-3xl sm:text-3xxl">{content}</p>
				)}
				{variant === "tao" ? (
					<Bittensor className="text-2xl min-[400px]:text-3xl sm:text-3xxl" />
				) : variant === "percentage" ? (
					<p className="text-2xl text-white min-[400px]:text-3xl sm:text-3xxl">%</p>
				) : (
					<></>
				)}
			</div>
		</div>
	);
}
