import { Text } from "@/components/elements/typography";
import cn from "@/lib/cn";

import { HiMiniStop } from "react-icons/hi2";
import { PieChart } from "./pie-chart";
import useWindowSize from "@/lib/hooks/use-window-size";

export default function TokenomicsGraph({
	totalSupply,
	circulatingSupply,
	unissued,
	stakedCirculatingSupply,
	freeCirculatingSupply,
	lockedCirculatingSupply,
}: {
	totalSupply: number;
	circulatingSupply: number;
	unissued: number;
	stakedCirculatingSupply: number;
	freeCirculatingSupply: number;
	lockedCirculatingSupply: number;
}) {
	const { isMobile } = useWindowSize();

	const supplyData = [
		{
			label: "Circulating Free",
			usage: freeCirculatingSupply,
			color: "text-[#EB5347]",
		},
		{
			label: "Circulating Delegated",
			usage: stakedCirculatingSupply,
			color: "text-ocean",
		},
		{
			label: "Circulating Locked",
			usage: lockedCirculatingSupply,
			color: "text-green",
		},
		{
			label: "Unissued",
			usage: unissued,
			color: "text-grayish",
		},
	];

	return (
		<div className="flex w-full flex-col items-center gap-4 rounded-[10px] border border-[#95959533] bg-[#2E2E2E33] p-4 xl:max-w-[650px] md:flex-row min-[400px]:gap-8 lg:px-20 min-[400px]:px-11 xl:px-11 min-[400px]:py-10">
			<div className="flex flex-1 flex-col justify-center gap-6 min-[400px]:gap-16">
				<div className="gap flex flex-col gap-5">
					<p className="text-3xl sm:text-3xxl">Supply Data</p>
					<div className="flex flex-col gap-1.5">
						{supplyData.map((item, index) => (
							<Text
								level={"base"}
								className={cn("flex items-center gap-2 text-[#9E9E9E]")}
								key={index}
							>
								<HiMiniStop className={item.color} />
								{item.label}
							</Text>
						))}
					</div>
				</div>
				<Text level={"sm"} className="text-[#606060]">
					The total issuance used in Taostats is taken directly from the
					substrate blockchain.
				</Text>
			</div>
			<div
				className={
					"h-[256px] w-[256px] min-[400px]:h-[300px] min-[400px]:w-[300px]"
				}
			>
				<PieChart
					width={isMobile ? 250 : 300}
					height={isMobile ? 250 : 300}
					data={supplyData.reverse()}
					totalSupply={totalSupply}
					circulatingSupply={circulatingSupply}
				/>
			</div>
		</div>
	);
}
