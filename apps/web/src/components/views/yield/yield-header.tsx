'use client';

import { Skeleton } from '@repo/ui/components';
import YieldCard from './yield-card';
import { useStats } from '@/lib/hooks';
import { tooltipDescription } from '@/lib/tooltip-description';

export default function YieldHeader() {
  const { data: stats, isPending } = useStats();

  return isPending ? (
    <div className='flex flex-col gap-6 md:min-w-[420px]'>
      {Array.from({ length: 3 }).map((_, index) => (
        <Skeleton className='h-20 w-full' key={index} />
      ))}
    </div>
  ) : (
    <div className='flex flex-col gap-6'>
      <div className='flex w-full min-w-0 flex-col gap-6 md:min-w-[300px] lg:min-w-[420px]'>
        <YieldCard
          title='Total Tao Staked'
          tooltip
          tooltipDescription={tooltipDescription.yield.totalTaoStaked}
          value={stats?.data[0].staked ?? ''}
          total={stats?.data[0].issued ?? ''}
          className='text-[#00DBBC]'
        />
        <YieldCard
          title='Total Root Stake'
          value={stats?.data[0].staked_root ?? ''}
          total={stats?.data[0].staked ?? ''}
          className='text-[#EB5347]'
        />
        <YieldCard
          title='Total Alpha Stake'
          value={stats?.data[0].staked_alpha ?? ''}
          total={stats?.data[0].staked ?? ''}
          className='text-[#EBC247]'
        />
      </div>
    </div>
  );
}
