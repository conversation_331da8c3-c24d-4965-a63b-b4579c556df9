import type { CellContext } from '@tanstack/react-table';
import type { TableData } from '@repo/types/website-api-types';
import { TaoWrapper } from '@/components/elements/tao-wrapper';

export const ColdkeyDailyTaoCell = ({
  row,
}: CellContext<TableData, unknown>) => (
  <TaoWrapper
    className='text-fire'
    info={row.original.daily_reward as string}
    minimumFractionDigits={0}
    maximumFractionDigits={4}
  />
);
