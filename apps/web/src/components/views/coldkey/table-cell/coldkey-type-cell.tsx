import type { CellContext } from '@tanstack/react-table';
import { Pickaxe, Shield } from 'lucide-react';
import type { TableData } from '@repo/types/website-api-types';
import { cn } from '@repo/ui/lib';

export const ColdkeyTypeCell = ({ row }: CellContext<TableData, unknown>) =>
  row.original.type !== undefined ? (
    <div className='flex w-fit items-center gap-2 rounded-lg bg-white/[0.04] px-3 py-2'>
      {(row.original.type as string) === 'VALIDATOR' ? (
        <Shield size={14} className='text-indigo-400' />
      ) : (
        <Pickaxe
          size={14}
          className={cn(
            (row.original.type as string) === 'MINER'
              ? 'text-[#00DBBC]'
              : 'text-[#F90]'
          )}
        />
      )}
    </div>
  ) : null;
