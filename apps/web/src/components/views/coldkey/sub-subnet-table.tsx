'use client';

import { use<PERSON><PERSON>back, useMemo, useState } from 'react';
import type { SortingState } from '@tanstack/react-table';
import type {
  ColumnSchema,
  Metagraph,
  TableData,
} from '@repo/types/website-api-types';
import { BubbleTable, But<PERSON>, Link, Text } from '@repo/ui/components';
import { appRoutes } from '@repo/ui/lib';
import {
  ColdkeyActiveCell,
  ColdkeyAxonCell,
  ColdkeyCell,
  ColdkeyConsensusCell,
  ColdkeyDailyTaoCell,
  ColdkeyDailyUsdCell,
  ColdkeyDividendsCell,
  ColdkeyEmissionCell,
  ColdkeyHotkeyCell,
  ColdkeyImmunityCell,
  ColdkeyIncentiveCell,
  ColdkeyStakeCell,
  ColdkeyTotalCell,
  ColdkeyTrustCell,
  ColdkeyTypeCell,
  ColdkeyUidCell,
  ColdkeyUpdatedCell,
  ColdkeyVtrustCell,
} from './table-cell';
import { getSorting } from '@/lib/utils';
import { useLatestPriceAtom } from '@/store/use-latest-price';

export default function SubSubnetTable({
  data,
  subnetID,
}: {
  data: Metagraph[];
  subnetID: string;
}) {
  const [sorting, setSorting] = useState<SortingState>([]);
  const { latestPrice: taoValue } = useLatestPriceAtom();

  const columns = useMemo<ColumnSchema[]>(
    () => [
      {
        id: 'type',
        header: 'Type',
        cell: ColdkeyTypeCell,
      },
      {
        id: 'uid',
        header: 'UID',
        cell: ColdkeyUidCell,
      },
      {
        id: 'stake',
        header: 'Stake (τ)',
        cell: ColdkeyStakeCell,
        sortingFn: 'alphanumeric',
      },
      {
        id: 'validator_trust',
        header: 'VTrust',
        cell: ColdkeyVtrustCell,
      },
      {
        id: 'trust',
        header: 'Trust',
        cell: ColdkeyTrustCell,
      },
      {
        id: 'consensus',
        header: 'Consensus',
        cell: ColdkeyConsensusCell,
      },
      {
        id: 'incentive',
        header: 'Incentive',
        cell: ColdkeyIncentiveCell,
      },
      {
        id: 'dividends',
        header: 'Dividends',
        cell: ColdkeyDividendsCell,
      },
      {
        id: 'emission',
        header: 'Emission(p)',
        cell: ColdkeyEmissionCell,
      },
      {
        id: 'updated',
        header: 'Updated',
        cell: ColdkeyUpdatedCell,
      },
      {
        id: 'axon_info',
        header: 'Axon',
        cell: ColdkeyAxonCell,
      },
      {
        id: 'hotkey',
        header: 'Hotkey',
        cell: ColdkeyHotkeyCell,
      },
      {
        id: 'coldkey',
        header: 'Coldkey',
        cell: ColdkeyCell,
      },
      {
        id: 'daily_reward',
        header: 'Daily 𝞃',
        cell: ColdkeyDailyTaoCell,
      },
      {
        id: 'daily_reward1',
        header: 'Daily $',
        cell: ColdkeyDailyUsdCell,
      },
      {
        id: 'total',
        header: 'Total $',
        cell: ColdkeyTotalCell,
      },
      {
        id: 'active',
        header: 'Active',
        cell: ColdkeyActiveCell,
      },
      {
        id: 'is_immunity_period',
        header: 'Immunity',
        cell: ColdkeyImmunityCell,
        sortingFn: 'text',
      },
    ],
    []
  );

  const tableData = useMemo(() => {
    let dailyReward = 0;
    let totalStake = 0;
    const temp: TableData[] = data
      .map(
        ({
          block_number: blockNumber,
          netuid,
          uid,
          stake,
          trust,
          validator_trust: validatorTrust,
          consensus,
          incentive,
          dividends,
          emission,
          active,
          hotkey,
          coldkey,
          axon,
          daily_reward: dailyRewardValue,
          is_immunity_period: isImmunityPeriod,
          updated,
        }) => {
          totalStake += Number(stake);
          dailyReward += Number(dailyRewardValue);
          return {
            block_number: blockNumber,
            netuid,
            uid,
            stake: Number(stake),
            total: Number(stake) * taoValue,
            trust,
            validator_trust: validatorTrust,
            type:
              Number(validatorTrust) > 0
                ? 'VALIDATOR'
                : isImmunityPeriod
                  ? 'IMMUNE MINER'
                  : 'MINER',
            consensus,
            incentive,
            dividends,
            emission,
            updated,
            active,
            hotkey: hotkey.ss58,
            coldkey: coldkey.ss58,
            axon_info: axon?.ip ? `${axon.ip}:${axon.port}` : '0.0.0.0',
            daily_reward: dailyRewardValue,
            daily_reward1: Number(dailyRewardValue) * taoValue,
            is_immunity_period: isImmunityPeriod,
          };
        }
      )
      .sort((a, b) => getSorting(a, b, sorting));

    temp.push({
      uid: 'Total',
      daily_reward: dailyReward,
      daily_reward1: dailyReward * taoValue,
      total: totalStake * taoValue,
    });

    return temp;
  }, [data, taoValue, sorting]);

  const handleFilterChange = useCallback(
    (filter: string, sortingValue: SortingState) => {
      setSorting(sortingValue);
    },
    []
  );

  return (
    <div className='space-y-2 px-4 sm:space-y-10 sm:px-10'>
      <Button asChild variant='link' className='text-ocean decoration-ocean'>
        <Link href={appRoutes.subnets.detail(subnetID)}>
          <Text level='mdTitle' className='text-ocean font-medium'>
            Subnet {subnetID}
          </Text>
        </Link>
      </Button>
      <BubbleTable
        columnSchemas={columns}
        rowData={tableData}
        onFilterChange={handleFilterChange}
      />
    </div>
  );
}
