'use client';

import React from 'react';
import { usePathname } from 'next/navigation';
import { walletFormat, useWindowSize } from '../../lib';
import { Link } from '../link';
import { Text } from '../text';

const Breadcrumbs: React.FC = () => {
  const { windowSize } = useWindowSize();
  const pathname = usePathname();
  const pathSegments = pathname.split('/').filter((segment) => segment !== '');

  const capitalizeFirstLetter = (string: string) => {
    return string
      .split(' ')
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  };

  return (
    <div className='flex h-14 w-full items-center justify-start gap-3 overflow-x-auto border-b border-neutral-900 px-2 md:px-10'>
      {pathSegments.map((segment: string, index: number) => {
        const isLast = index === pathSegments.length - 1;
        let href = `/${pathSegments.slice(0, index + 1).join('/')}`;
        if (!href.endsWith('s') && Number.isNaN(Number(segment))) {
          // If the href does not end in S and segment is not a number then append an S
          href += 's';
        }

        return (
          <React.Fragment key={segment}>
            {isLast ? (
              <Text level='sm'>
                {(windowSize.width ?? 0) > 596
                  ? capitalizeFirstLetter(segment.replaceAll('_', ' '))
                  : walletFormat(
                      capitalizeFirstLetter(segment.replaceAll('_', ' '))
                    )}
              </Text>
            ) : (
              <>
                <Link href={href}>
                  <Text
                    level='sm'
                    className='hover:text-ocean truncate break-words'
                  >
                    {capitalizeFirstLetter(segment.replaceAll('_', ' '))}
                  </Text>
                </Link>
                <Text level='sm' className='opacity-40'>
                  /
                </Text>
              </>
            )}
          </React.Fragment>
        );
      })}
    </div>
  );
};

export default Breadcrumbs;
